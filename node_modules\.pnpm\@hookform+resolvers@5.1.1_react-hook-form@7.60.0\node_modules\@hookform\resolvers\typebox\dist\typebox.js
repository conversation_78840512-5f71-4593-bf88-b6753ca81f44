var e=require("@hookform/resolvers"),r=require("@sinclair/typebox/compiler"),s=require("@sinclair/typebox/value"),o=require("react-hook-form");function a(e,r){for(var s={};e.length;){var a=e[0],t=a.type,i=a.message,l=a.path.substring(1).replace(/\//g,".");if(s[l]||(s[l]={message:i,type:""+t}),r){var n=s[l].types,u=n&&n[""+t];s[l]=o.appendErrors(l,r,s,""+t,u?[].concat(u,a.message):a.message)}e.shift()}return s}exports.typeboxResolver=function(o){return function(t,i,l){try{var n=Array.from(o instanceof r.TypeCheck?o.Errors(t):s.Value.Errors(o,t));return l.shouldUseNativeValidation&&e.validateFieldsNatively({},l),Promise.resolve(n.length?{values:{},errors:e.toNestErrors(a(n,!l.shouldUseNativeValidation&&"all"===l.criteriaMode),l)}:{errors:{},values:t})}catch(e){return Promise.reject(e)}}};
//# sourceMappingURL=typebox.js.map
